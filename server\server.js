// server/server.js

const WebSocket = require('ws');
const GameState = require('./gameState.js');
const { v4: uuidv4 } = require('uuid');

const PORT = 8080;
const wss = new WebSocket.Server({ port: PORT });
const game = new GameState();

// Map of playerId -> ws
const clients = new Map();
// Map of ws -> playerId
const wsToId = new Map();
const playerConnections = new Map(); // playerId -> Set of ws
const pendingRemovals = new Map(); // playerId -> timeoutId

let lastBroadcastPlayers = '';

console.log(`WebSocket server running on ws://localhost:${PORT}`);

wss.on('connection', (ws) => {
  let playerId = null;
  let nickname = null;

  ws.on('message', (data) => {
    let msg;
    try {
      msg = JSON.parse(data);
    } catch (e) {
      return;
    }
    // First message must be nickname (optionally with playerId)
    if (!playerId && msg.type === 'nickname') {
      nickname = (msg.nickname || '').trim().slice(0, 16);
      if (!nickname) return;
      // Cancel any pending removal for this player BEFORE addPlayer
      if (msg.playerId && pendingRemovals.has(msg.playerId)) {
        clearTimeout(pendingRemovals.get(msg.playerId));
        pendingRemovals.delete(msg.playerId);
      }
      // Try to reuse playerId if provided and not already taken
      let player = null;
      if (msg.playerId) {
        player = game.players.find(p => p.id === msg.playerId);
        if (player) {
          player.alive = true;
          player.nickname = nickname;
        }
      }
      if (!player) {
        player = game.addPlayer(nickname);
      }
      if (!player) {
        ws.send(JSON.stringify({ type: 'error', error: 'Game full' }));
        ws.close();
        return;
      }
      playerId = player.id;
      clients.set(playerId, ws);
      wsToId.set(ws, playerId);
      // Track this connection for the player
      if (!playerConnections.has(playerId)) playerConnections.set(playerId, new Set());
      playerConnections.get(playerId).add(ws);
      // Send playerId to client
      ws.send(JSON.stringify({ type: 'playerId', id: playerId }));
      return;
    }
    if (!playerId) return;
    // Handle actions
    if (msg.type === 'action') {
      game.handleAction(playerId, msg.action);
    } else if (msg.type === 'chat') {
      game.handleChat(playerId, msg.text);
    }
  });

  ws.on('close', () => {
    if (playerId) {
      // Remove this connection from the player's set
      const conns = playerConnections.get(playerId);
      if (conns) {
        conns.delete(ws);
        if (conns.size === 0) {
          // Check for immediate win condition before delayed removal
          // This handles the case where a disconnect should trigger an immediate win
          const currentPlayerCount = game.players.length;
          const gamePhase = game.phase;

          // If we're in a multiplayer game (lobby or active game) and this disconnect
          // would leave only 1 player, trigger immediate win condition check
          if ((gamePhase === 'game' || gamePhase === 'lobby') && currentPlayerCount === 2) {
            // Immediately remove the player and check win condition
            game.removePlayer(playerId);
            playerConnections.delete(playerId);
            // Don't set up the delayed removal since we've already removed the player
          } else {
            // Normal delayed removal for other cases (allows quick reconnects)
            const timeoutId = setTimeout(() => {
              game.removePlayer(playerId);
              playerConnections.delete(playerId);
              pendingRemovals.delete(playerId);
            }, 2000);
            pendingRemovals.set(playerId, timeoutId);
          }
        }
      }
      clients.delete(playerId);
      wsToId.delete(ws);
    }
  });
});

// Game loop: update and broadcast state
setInterval(() => {
  game.update();
  const state = game.getState();
  const playerListStr = state.players.map(p => p.nickname).join(',');
  if (playerListStr !== lastBroadcastPlayers) {
    console.log('[Broadcast] Players:', playerListStr);
    lastBroadcastPlayers = playerListStr;
  }
  const msg = JSON.stringify({ type: 'state', state });
  for (const ws of wss.clients) {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(msg);
    }
  }
}, 1000 / 30); // 30 FPS broadcast
