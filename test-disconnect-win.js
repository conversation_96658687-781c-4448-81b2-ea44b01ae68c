// Test script for disconnect win condition
// This script tests both scenarios: lobby disconnect and active game disconnect

const WebSocket = require('ws');

async function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function testLobbyDisconnectWin() {
  console.log('\n=== Testing Lobby Disconnect Win Condition ===');
  
  // Create two players
  const player1 = new WebSocket('ws://localhost:8080');
  const player2 = new WebSocket('ws://localhost:8080');
  
  let player1Winner = false;
  let player2Winner = false;
  
  player1.on('open', () => {
    console.log('Player 1 connected');
    player1.send(JSON.stringify({ type: 'nickname', nickname: 'TestPlayer1' }));
  });
  
  player2.on('open', () => {
    console.log('Player 2 connected');
    player2.send(JSON.stringify({ type: 'nickname', nickname: 'TestPlayer2' }));
  });
  
  player1.on('message', (data) => {
    const msg = JSON.parse(data);
    if (msg.type === 'state' && msg.state.phase === 'end' && msg.state.winner === 'TestPlayer1') {
      console.log('✅ Player 1 won due to Player 2 disconnect in lobby!');
      player1Winner = true;
    }
  });
  
  player2.on('message', (data) => {
    const msg = JSON.parse(data);
    if (msg.type === 'state' && msg.state.phase === 'end' && msg.state.winner === 'TestPlayer2') {
      console.log('✅ Player 2 won due to Player 1 disconnect in lobby!');
      player2Winner = true;
    }
  });
  
  // Wait for both players to connect and be in lobby
  await sleep(2000);
  
  // Disconnect player 2 while in lobby
  console.log('Disconnecting Player 2 from lobby...');
  player2.close();
  
  // Wait for win condition to trigger
  await sleep(1000);
  
  if (player1Winner) {
    console.log('✅ Lobby disconnect win condition test PASSED');
  } else {
    console.log('❌ Lobby disconnect win condition test FAILED');
  }
  
  player1.close();
  await sleep(2000); // Wait for cleanup
}

async function testGameDisconnectWin() {
  console.log('\n=== Testing Active Game Disconnect Win Condition ===');
  
  // Create two players
  const player1 = new WebSocket('ws://localhost:8080');
  const player2 = new WebSocket('ws://localhost:8080');
  
  let player1Winner = false;
  let player2Winner = false;
  let gameStarted = false;
  
  player1.on('open', () => {
    console.log('Player 1 connected');
    player1.send(JSON.stringify({ type: 'nickname', nickname: 'GamePlayer1' }));
  });
  
  player2.on('open', () => {
    console.log('Player 2 connected');
    player2.send(JSON.stringify({ type: 'nickname', nickname: 'GamePlayer2' }));
  });
  
  player1.on('message', (data) => {
    const msg = JSON.parse(data);
    if (msg.type === 'state') {
      if (msg.state.phase === 'game' && !gameStarted) {
        console.log('Game started! Waiting a moment before disconnect...');
        gameStarted = true;
        // Wait a bit then disconnect player 2
        setTimeout(() => {
          console.log('Disconnecting Player 2 during active game...');
          player2.close();
        }, 1000);
      }
      if (msg.state.phase === 'end' && msg.state.winner === 'GamePlayer1') {
        console.log('✅ Player 1 won due to Player 2 disconnect during game!');
        player1Winner = true;
      }
    }
  });
  
  player2.on('message', (data) => {
    const msg = JSON.parse(data);
    if (msg.type === 'state' && msg.state.phase === 'end' && msg.state.winner === 'GamePlayer2') {
      console.log('✅ Player 2 won due to Player 1 disconnect during game!');
      player2Winner = true;
    }
  });
  
  // Wait for both players to connect and game to start
  await sleep(25000); // Wait for lobby countdown (20s wait + 10s countdown)
  
  // Wait for win condition to trigger
  await sleep(3000);
  
  if (player1Winner) {
    console.log('✅ Active game disconnect win condition test PASSED');
  } else {
    console.log('❌ Active game disconnect win condition test FAILED');
  }
  
  player1.close();
  await sleep(2000); // Wait for cleanup
}

async function runTests() {
  console.log('Starting disconnect win condition tests...');
  console.log('Make sure the server is running on ws://localhost:8080');
  
  try {
    await testLobbyDisconnectWin();
    await testGameDisconnectWin();
    console.log('\n=== All tests completed ===');
  } catch (error) {
    console.error('Test error:', error);
  }
  
  process.exit(0);
}

runTests();
